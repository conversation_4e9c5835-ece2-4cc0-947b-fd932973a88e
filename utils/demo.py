from spiga.inference.config import ModelConfig
from spiga.inference.framework import SPIGAFramework
import cv2
import json

# Process image
dataset = '300wpublic'
processor = SPIGAFramework(ModelConfig(dataset))
image = cv2.imread("assets/colab/image_sportsfan.jpg")
with open('assets/colab/bbox_sportsfan.json') as jsonfile:
    bbox = json.load(jsonfile)['bbox']

features = processor.inference(image, [bbox])
print(features)
