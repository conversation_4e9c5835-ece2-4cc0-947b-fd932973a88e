albucore==0.0.23
albumentations==2.0.0
annotated-types==0.7.0
certifi==2024.12.14
charset-normalizer==3.4.1
coloredlogs==15.0.1
contourpy==1.3.0
cycler==0.12.1
Cython==3.0.11
easydict==1.13
eval_type_backport==0.2.2
filelock==3.13.1
flatbuffers==24.12.23
fonttools==4.55.3
fsspec==2024.2.0
humanfriendly==10.0
idna==3.10
imageio==2.36.1
importlib_resources==6.5.2
insightface==0.7.3
Jinja2==3.1.3
joblib==1.4.2
kiwisolver==1.4.7
lazy_loader==0.4
MarkupSafe==2.1.5
matplotlib==3.9.4
mpmath==1.3.0
networkx==3.2.1
numpy==1.26.3
# nvidia-cublas-cu12==********
# nvidia-cuda-cupti-cu12==12.1.105
# nvidia-cuda-nvrtc-cu12==12.1.105
# nvidia-cuda-runtime-cu12==12.1.105
# nvidia-cudnn-cu12==********
# nvidia-cufft-cu12==*********
# nvidia-curand-cu12==**********
# nvidia-cusolver-cu12==**********
# nvidia-cusparse-cu12==**********
# nvidia-nccl-cu12==2.21.5
# nvidia-nvjitlink-cu12==12.1.105
# nvidia-nvtx-cu12==12.1.105
onnx==1.17.0
onnxruntime-gpu==1.19.2
opencv-python==*********
opencv-python-headless==*********
packaging==24.2
pillow==10.2.0
pip==24.2
prettytable==3.12.0
protobuf==5.29.3
pydantic==2.10.5
pydantic_core==2.27.2
pyparsing==3.2.1
python-dateutil==2.9.0.post0
PyYAML==6.0.2
requests==2.32.3
scikit-image==0.24.0
scikit-learn==1.6.1
scipy==1.13.1
setuptools==75.1.0
simsimd==6.2.1
six==1.17.0
stringzilla==3.11.3
sympy==1.13.1
threadpoolctl==3.5.0
tifffile==2024.8.30
# torch==2.5.1+cu121
# torchaudio==2.5.1+cu121
# torchvision==0.20.1+cu121
tqdm==4.67.1
triton==3.1.0
typing_extensions==4.12.2
urllib3==2.3.0
wcwidth==0.2.13
wheel==0.44.0
zipp==3.21.0
