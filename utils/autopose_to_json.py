import cv2
import os
import json
import pandas as pd
import numpy as np
import torch

import onnx
import onnxruntime as ort
from pathlib import Path
from scipy.spatial.transform import Rotation
from tqdm import tqdm
from typing import Dict, Tuple, Optional, List
from datetime import datetime
from PIL import Image
import torchvision.transforms as transforms
import multiprocessing
from Retinaface import Retinaface
from spiga.inference.config import ModelConfig
from spiga.inference.framework import SPIGAFramework
from gaze_tr import model
import cv2
import json
from math import cos, sin


class BBox(object):
    # bbox is a list of [left, right, top, bottom]
    def __init__(self, bbox):
        self.left = bbox[0]
        self.right = bbox[1]
        self.top = bbox[2]
        self.bottom = bbox[3]
        self.x = bbox[0]
        self.y = bbox[2]
        self.w = bbox[1] - bbox[0]
        self.h = bbox[3] - bbox[2]

    # scale to [0,1]
    def projectLandmark(self, landmark):
        landmark_= np.asarray(np.zeros(landmark.shape))     
        for i, point in enumerate(landmark):
            landmark_[i] = ((point[0]-self.x)/self.w, (point[1]-self.y)/self.h)
        return landmark_

    # landmark of (5L, 2L) from [0,1] to real range
    def reprojectLandmark(self, landmark):
        landmark_= np.asarray(np.zeros(landmark.shape)) 
        for i, point in enumerate(landmark):
            x = point[0] * self.w + self.x
            y = point[1] * self.h + self.y
            landmark_[i] = (x, y)
        return landmark_

class AutoposeDataProcessor:
    def __init__(self, data_root: str, models_dir: str = "models", save_annotated_images: bool = True):
        self.data_root = Path(data_root)
        self.models_dir = Path(models_dir)
        self.save_annotated_images = save_annotated_images
        # Create directory for annotated images
        if self.save_annotated_images:
            self.annotated_images_dir = self.data_root / "annotated_images"
            self.annotated_images_dir.mkdir(exist_ok=True)
        
        # Initialize models
        self.initialize_models()
    
    def initialize_models(self):
        """Initialize face detection, landmark and gaze models using OpenVINO"""
        try:
            self.retina_face_detector = Retinaface.Retinaface()
            self.landmark_detector = SPIGAFramework(ModelConfig("wflw"))
            self.gaze_detector = model.load_gazetr_eth_model(model_path="GazeTR-H-ETH.pt", device='cpu')
            self.models_loaded = True
        except Exception as e:
            print(f"Could not initialize models: {str(e)}")
            self.models_loaded = False

    def preprocess_image(self, image: np.ndarray, target_shape: Tuple[int, int, int, int]) -> np.ndarray:
        """Preprocess image for OpenVINO model input"""
        # Resize image to model input dimensions
        height, width = target_shape[2], target_shape[3]
        resized_image = cv2.resize(image, (width, height))
        
        # Change data layout from HWC to NCHW
        preprocessed_image = resized_image.transpose((2, 0, 1))
        preprocessed_image = preprocessed_image.reshape(target_shape)
        
        return preprocessed_image

    def detect_face(self, image: np.ndarray) -> Optional[List[float]]:
        """Detect face in image using OpenVINO face detection model"""
        if not self.models_loaded:
            return None
        features = self.retina_face_detector(image)
        faces_rect = []
        if len(features) == 0:
            return None
        for feature in features:
            faces_rect.append([float(x) for x in feature])
        if faces_rect[0][5] < 0.9:
            return None
        return faces_rect[0]

    def detect_landmarks(self, image: np.ndarray, face_bbox: List[float]) -> Optional[np.ndarray]:
        
        """Detect facial landmarks using OpenVINO landmarks model"""
        if not self.models_loaded:
            return None
        features = self.landmark_detector.inference(image, [face_bbox])
        if features is None:
            return None
        else :
            landmarks = [features["landmarks"][0][x] for x in range(60, 88)]
            landmarks.extend([features["landmarks"][0][89]])
            landmarks.extend([features["landmarks"][0][90]])
            landmarks.extend([features["landmarks"][0][91]])
            landmarks.extend([features["landmarks"][0][93]])
            landmarks.extend([features["landmarks"][0][94]])
            landmarks.extend([features["landmarks"][0][95]])
            landmarks.extend([features["landmarks"][0][96]])
            landmarks.extend([features["landmarks"][0][97]])
            landmarks.extend([features["landmarks"][0][54]])

        return landmarks
    
    def detect_gaze(self, image: np.ndarray, face_bbox: List[float]) -> Optional[np.ndarray]:
        if not self.models_loaded:
            return None
        x_min, y_min, x_max, y_max = map(int, face_bbox[:4])
        face_crop = image[y_min:y_min+y_max, x_min:x_min+x_max]
        if face_crop.size == 0:
            return None
        pitch, yaw = self.gaze_detector.run({'face': face_crop})
        print(pitch, yaw)
        return pitch, yaw
    

    def draw_annotations(self, image: np.ndarray, face_bbox: List[float], landmarks_data: List[float], 
                        head_pose: Dict, subject_id: int, frame_num: int, nose_lm: List[float]) -> np.ndarray:
        """Draw annotations on image including face bbox, landmarks, and head pose info"""
        annotated_image = image.copy()
        
        # Draw face bounding box
        if face_bbox is not None and len(face_bbox) >= 4:
            x, y, w, h = map(int, face_bbox[:4])
            cv2.rectangle(annotated_image, (x, y), (x+w, y+h), (0, 255, 0), 2)
            
            # Add confidence score if available
            if len(face_bbox) > 4:
                confidence = face_bbox[4]
                cv2.putText(annotated_image, f"Conf: {confidence:.2f}", 
                           (x, y-10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)
        
        # Draw landmarks
        if landmarks_data is not None and len(landmarks_data) >= 6:  # At least 2 points
            # Convert landmarks back to points (assuming they're in groups of 3: x, y, visibility)
            landmarks_points = []
            for i in range(0, len(landmarks_data), 3):
                if i + 1 < len(landmarks_data):
                    x, y = landmarks_data[i], landmarks_data[i+1]
                    landmarks_points.append((int(x), int(y)))
            
            # Draw landmark points
            for i, (x, y) in enumerate(landmarks_points):
                cv2.circle(annotated_image, (x, y), 2, (255, 0, 0), -1)
                # Optionally add landmark numbers
                cv2.putText(annotated_image, str(i), (x+3, y-3), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.3, (255, 0, 0), 1)
        
        # Add head pose information
        if head_pose is not None:
            y_offset = 30
            info_texts = [
                f"Subject: {subject_id}, Frame: {frame_num}",
                f"Yaw: {head_pose.get('yaw', 0):.1f} deg",
                f"Pitch: {head_pose.get('pitch', 0):.1f} deg",
                f"Roll: {head_pose.get('roll', 0):.1f} deg"
            ]
            
            for i, text in enumerate(info_texts):
                cv2.putText(annotated_image, text, (10, y_offset + i*20), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
        
        # Draw coordinate axes for head pose visualization
        if head_pose is not None and face_bbox is not None:
            self.draw_head_pose_axes(annotated_image, face_bbox, head_pose, nose_lm)
            self.draw_gaze(annotated_image, face_bbox, head_pose["gaze"][0], head_pose["gaze"][1])
        
        return annotated_image
    
    def draw_head_pose_axes(self, image: np.ndarray, face_bbox: List[float], head_pose: Dict, nose_lm: List[float]):
        """Draw 3D coordinate axes representing head pose"""
        if len(face_bbox) < 4:
            return
            
        # Get face center
        x1, y1, w, h = map(int, face_bbox[:4])
        x2 = x1 + w
        y2 = y1 + h
        center_x = nose_lm[9]
        center_y = nose_lm[10]
       
        
        # Get rotation angles
        yaw = np.radians(head_pose.get('yaw', 0))
        pitch = np.radians(head_pose.get('pitch', 0))
        roll = np.radians(head_pose.get('roll', 0))

        tdx, tdy = (center_x, center_y)
        size = 50

       # X-Axis pointing to right. drawn in red
        x1 = size * (cos(yaw) * cos(roll)) + tdx
        y1 = size * (cos(pitch) * sin(roll) + cos(roll) * sin(pitch) * sin(yaw)) + tdy

        # Y-Axis | drawn in green
        #        v
        x2 = size * (-cos(yaw) * sin(roll)) + tdx
        y2 = size * (cos(pitch) * cos(roll) - sin(pitch) * sin(yaw) * sin(roll)) + tdy

        # Z-Axis (out of the screen) drawn in blue
        x3 = size * (sin(yaw)) + tdx
        y3 = size * (-cos(yaw) * sin(pitch)) + tdy



        cv2.line(image, (int(tdx), int(tdy)), (int(x1),int(y1)),(0,0,255),3)
        cv2.line(image, (int(tdx), int(tdy)), (int(x2),int(y2)),(0,255,0),3)
        cv2.line(image, (int(tdx), int(tdy)), (int(x3),int(y3)),(255,0,0),4)
    
    def draw_gaze(self,frame, bbox, pitch, yaw, thickness=2, color=(0, 0, 255)):
        """Draws gaze direction on a frame given bounding box and gaze angles."""
        # Unpack bounding box coordinates
        x_min, y_min, x_max, y_max = map(int, bbox[:4])

        # Calculate center of the bounding box
        x_center = (x_min + x_max) // 2
        y_center = (y_min + y_max) // 2

        # Handle grayscale frames by converting them to BGR
        if len(frame.shape) == 2 or frame.shape[2] == 1:
            frame = cv2.cvtColor(frame, cv2.COLOR_GRAY2BGR)

        # Calculate the direction of the gaze
        length = x_max - x_min
        pitch = np.radians(pitch)
        yaw = np.radians(yaw)
        dx = int(-length * np.sin(pitch) * np.cos(yaw))
        dy = int(-length * np.sin(yaw))

        point1 = (x_center, y_center)
        point2 = (x_center + dx, y_center + dy)

        # Draw gaze direction
        cv2.circle(frame, (x_center, y_center), radius=4, color=color, thickness=-1)
        cv2.arrowedLine(
            frame,
            point1,
            point2,
            color=color,
            thickness=thickness,
            line_type=cv2.LINE_AA,
            tipLength=0.25
        )


    def save_annotated_image(self, annotated_image: np.ndarray, subject_id: int, frame_num: int):
        """Save annotated image to disk"""
        if not self.save_annotated_images:
            return
        
        
        # Create subject directory
        subject_dir = self.annotated_images_dir / f"Subject_{subject_id}"
        subject_dir.mkdir(exist_ok=True)

        # Save annotated image
        output_path = subject_dir / f"Subject_{subject_id}_{frame_num}_annotated.png"
        cv2.imwrite(str(output_path), annotated_image)

    def detect_face_and_landmarks(self, image: np.ndarray, head_pose: Dict) -> Tuple[Optional[Dict], Optional[Dict], Optional[Dict]]:
        """Detect face, landmarks, head pose and gaze in image using OpenVINO models"""
        if not self.models_loaded or image is None:
            return None, None, None, None
            
    
        # Detect face
        face_data = self.detect_face(image)
        if face_data is None:
            return None, None, None, None
        
        face_bbox = face_data[:4]
        face_bbox[2] = face_bbox[2] - face_bbox[0]
        face_bbox[3] = face_bbox[3] - face_bbox[1]
        # Detect landmarks model
        landmarks = self.detect_landmarks(image, face_bbox)
        if landmarks is None:
            return face_bbox, None, None, None
        # Create landmarks data
        landmarks_data = self.convert_to_coco_keypoints(landmarks)

        return face_bbox, landmarks_data, head_pose, face_data

    def convert_to_coco_keypoints(self,landmarks):
        landmarks_data = []
        for landmark in landmarks:
            x, y = landmark[0], landmark[1]
            landmarks_data.extend([x, y, 2])
        return landmarks_data

    def process_csv(self, subject_id: int, csv_path: str, start_annotation_id: int):
        """Process CSV file and return lists of images and annotations in COCO format"""
        df = pd.read_csv(csv_path)
        images = []
        annotations = []
        annotation_id = start_annotation_id
       
        for index, row in tqdm(df.iterrows(), total=len(df), desc=f"Processing Subject_{subject_id}"):
            frame_num = int(row['FrameNumber'])
            image_path = self.data_root / f"train/Subject_{subject_id}/Subject_{subject_id}_{frame_num}.png"

            # Extract rotation matrix (3x3) from R_Head2Cam_1 to R_Head2Cam_9
            R = np.array([
                [float(row['R_Head2Cam_1']), float(row['R_Head2Cam_2']), float(row['R_Head2Cam_3'])],
                [float(row['R_Head2Cam_4']), float(row['R_Head2Cam_5']), float(row['R_Head2Cam_6'])],
                [float(row['R_Head2Cam_7']), float(row['R_Head2Cam_8']), float(row['R_Head2Cam_9'])]
            ])
            
            # Extract translation vector from t_Head_in_Cam
            T = np.array([
                float(row['t_Head_in_Cam_1']),
                float(row['t_Head_in_Cam_2']),
                float(row['t_Head_in_Cam_3'])
            ])
            
            # Calculate Euler angles (in degrees)
            euler_angles = Rotation.from_matrix(R).as_euler('zyx', degrees=True)
            roll, yaw, pitch = map(float, euler_angles)  # Convert to native Python float
            head_pose =  {"rotation_matrix": R.tolist(),  # Convert numpy array to list
                        "translation_vector": T.tolist(),  # Convert numpy array to list
                        "yaw": yaw, "pitch": pitch, "roll": roll}
            
            if not image_path.exists():
                print(f"Warning: Image not found: {image_path}")
                continue
            
            image = cv2.imread(str(image_path))
            if image is None:
                print(image_path)
            shape = image.shape
            
            face_data, landmarks_data = None, None
            if self.models_loaded and image is not None:
                face_data, landmarks_data, head_pose, nose_lm = self.detect_face_and_landmarks(image, head_pose)
                if face_data is not None:
                    gaze = self.detect_gaze(image, face_data)
                    if gaze is not None:
                        head_pose["gaze"] = gaze
            
            if face_data is None:
                continue   
            # Create and save annotated image
            if self.save_annotated_images and image is not None:
                annotated_image = self.draw_annotations(image, face_data, landmarks_data, 
                                                      head_pose, subject_id, frame_num, nose_lm)
                self.save_annotated_image(annotated_image, subject_id, frame_num)
                
            # Add image info
            images.append({
                "id": frame_num,
                "file_name": f"Subject_{subject_id}/Subject_{subject_id}_{frame_num}.png",
                "subject_id": subject_id,
                "width": int(shape[1]),  # Convert to int
                "height": int(shape[0])  # Convert to int
            })
            
            # Add annotation info
            annotation = {
                "id": annotation_id,  # Add unique annotation ID
                "image_id": frame_num,
                "category_id": 1,
                "subject_id": subject_id,
                "valid_target": bool(row['Target_valid']),
                "head_pose": head_pose
            }
            
            # Add OpenVINO detection results if available
            annotation["bbox"] = face_data
            annotation["iscrowd"] = 0
            annotation["area"]=face_data[2]*face_data[3]
            annotation["keypoints"] = landmarks_data
            
            annotations.append(annotation)
            annotation_id += 1
            
        return images, annotations, annotation_id
    
    def save_json(self, data: Dict, output_path: str):
        """Save processed data to JSON file"""
        with open(output_path, 'w') as f:
            json.dump(data, f, indent=4,ensure_ascii=False)
        print(f"Dataset saved to {output_path}")

def main():
    # Configure paths
    data_root = "../../data/Autopose"  # Adjust this path
    models_dir = "models"
    gt_dir = os.path.join(data_root, "gt")
    
    # Get all CSV files and sort them by subject ID
    gt_path = Path(gt_dir)
    csv_files = list(gt_path.glob("Subject_*_Groundtruth.csv"))
    csv_files.sort(key=lambda x: int(x.stem.split('_')[1]))
    csv_files = [csv_files[1]]
    
    # Prepare arguments for processing (added save_annotated_images flag)
    args_list = [(str(csv_file), str(data_root), str(models_dir), True) for csv_file in csv_files]
    
    # Use multiprocessing to process subjects in parallel
    with multiprocessing.Pool(processes=multiprocessing.cpu_count()) as pool:
        pool.map(process_subject_in_worker, args_list)
    
    print(f"Processed {len(csv_files)} subjects in parallel")

# This function needs to be at module level for multiprocessing
def process_subject_in_worker(args):
    """Worker function that creates processor instance and processes a subject"""
    csv_file, data_root, models_dir, save_annotated_images = args
    # Create a new processor instance in each worker
    processor = AutoposeDataProcessor(data_root, models_dir, save_annotated_images)
    
    # Extract subject ID from filename
    subject_id = int(Path(csv_file).stem.split('_')[1])
    print(f"Processing Subject_{subject_id}")
    
    # Initialize annotation ID counter for this subject
    start_annotation_id = 1
    
    # Process the CSV file
    images, annotations, _ = processor.process_csv(subject_id, csv_file, start_annotation_id)
    
    # Create dataset for this subject
    subject_dataset = {
        "info": {
            "description": f"Autopose Dataset - Subject {subject_id}",
            "url": "",
            "version": "1.0",
            "year": datetime.now().year,
            "contributor": "Autopose",
            "date_created": datetime.now().strftime("%Y-%m-%d")
        },
        "images": images,
        "annotations": annotations,
        "categories": [{
            "id": 1,
            "name": "person",
            "supercategory": "person"
        }]
    }
    
    # Save individual subject JSON
    subject_output_path = os.path.join(data_root, f"autopose_subject_{subject_id}.json")
    processor.save_json(subject_dataset, subject_output_path)

if __name__ == "__main__":
    main()
