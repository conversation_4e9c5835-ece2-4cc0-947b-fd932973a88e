import fiftyone as fo
import json
import cv2
import os
import numpy as np

# Load your COCO formatted JSON
with open("../data/Autopose/autopose_subject_2.json", "r") as f:
    coco_data = json.load(f)

# Delete dataset if it already exists
if fo.dataset_exists("head_pose_visualization"):
    fo.delete_dataset("head_pose_visualization")

dataset = fo.Dataset(name="head_pose_visualization")

# Process each image in the COCO data
for img_info in coco_data["images"]:
    img_id = img_info["image_id"]
    img_path = img_info["file_name"]
    width = img_info["width"]
    height = img_info["height"]

    # Create a FiftyOne sample
    sample = fo.Sample(filepath=os.path.join("../data/Autopose/train", img_info["file_name"]))
    
    # Initialize lists for annotations
    detections = []
    keypoints_list = []
    head_pose_axes = []

    # Get annotations for this image
    annotations = [ann for ann in coco_data["annotations"] if ann["image_id"] == img_id]
    rel_landmarks = None
    for ann in annotations:
        if ann.get("bbox") is None:
            continue
        # Convert bounding box to FiftyOne format (relative coordinates)
        x, y, w, h, _ = ann["bbox"]
        
        rel_bbox = [
            x / width,
            y / height,
            (w-x) / width,
            (h-y) / height
        ]
        
        detection = fo.Detection(
            label="face",
            bounding_box=rel_bbox,
            confidence=ann["bbox"][4],
        )
        detections.append(detection)
        
        # Convert facial landmarks to Keypoints
        keypoints = ann["keypoints"]
        landmarks = [[keypoints[i], keypoints[i+1]] for i in range(0, len(keypoints), 2)]
        rel_landmarks = [(x / width, y / height) for x, y in landmarks]
        
        # Head pose visualization
        rotation_matrix = np.array(ann["head_pose"]["rotation_matrix"])
        translation_vector = np.array(ann["head_pose"]["translation_vector"])
        
        # Convert rotation matrix to rotation vector
        rotation_vector, _ = cv2.Rodrigues(rotation_matrix)
        
        # Define 3D axis points
        points_3d = np.array([
            [0, 0, 0],     # Origin
            [50, 0, 0],    # X-axis
            [0, 50, 0],    # Y-axis
            [0, 0, 50]     # Z-axis
        ], dtype=np.float32)
        
        # Camera matrix
        focal_length = 500
        image_center = (width // 2, height // 2)
        camera_matrix = np.array([
            [focal_length, 0, image_center[0]],
            [0, focal_length, image_center[1]],
            [0, 0, 1]
        ], dtype=np.float32)
        
        # Project 3D points to 2D
        points_2d, _ = cv2.projectPoints(
            points_3d,
            rotation_vector,
            translation_vector,
            camera_matrix,
            None
        )
        points_2d = points_2d.reshape(-1, 2)
        
        # Convert to relative coordinates
        origin = (points_2d[0][0]/width, points_2d[0][1]/height)
        x_end = (points_2d[1][0]/width, points_2d[1][1]/height)
        y_end = (points_2d[2][0]/width, points_2d[2][1]/height)
        z_end = (points_2d[3][0]/width, points_2d[3][1]/height)

        print(origin, x_end, y_end, z_end)
        
        # Create axis polylines
        head_pose_axes.extend([
            fo.Polyline(
                label="Pitch",
                points=[[origin, x_end]],
                closed=False,
                filled=False,
                line_width=4,
                color="#ff0000",
            ),
            fo.Polyline(
                label="Roll",
                points=[[origin, y_end]],
                closed=False,
                filled=False,
                line_width=4,
                color="#0000ff",
            ),
            fo.Polyline(
                label="Yaw",
                points=[[origin, z_end]],
                closed=False,
                filled=False,
                line_width=4,
                color="#00ff00",
            )
        ])
        
        '''gaze_vector = None
        if "gaze_vector" in ann["head_pose"]:
            gaze_vector = np.array(ann["head_pose"]["gaze_vector"])

        # Get eye landmarks
        left_eye_landmarks = None
        right_eye_landmarks = None
        if "facial_landmarks" in ann and "points" in ann["facial_landmarks"] and gaze_vector is not None:
            landmarks = ann["facial_landmarks"]["points"]
            
            # Calculate left eye center
            left_eye_center_x = (landmarks[4][0] +  landmarks[7][0] ) / 2
            left_eye_center_y = (landmarks[4][1] +  landmarks[7][1] ) / 2
            left_eye_center = (left_eye_center_x, left_eye_center_y)
            
            # Calculate right eye center
            right_eye_center_x = (landmarks[10][0] +  landmarks[13][0] ) / 2
            right_eye_center_y = (landmarks[10][1] +  landmarks[13][1] ) / 2
            right_eye_center = (right_eye_center_x, right_eye_center_y)
            
            # Calculate gaze line endpoints (scale gaze vector for visualization)
            gaze_scale = 100.0  # Adjust this value to change line length
            
            # Left eye gaze line
            left_gaze_end_x = left_eye_center_x + gaze_vector[0] * gaze_scale
            left_gaze_end_y = left_eye_center_y + gaze_vector[1] * gaze_scale
            left_gaze_end = (left_gaze_end_x, left_gaze_end_y)
            
            # Right eye gaze line
            right_gaze_end_x = right_eye_center_x + gaze_vector[0] * gaze_scale
            right_gaze_end_y = right_eye_center_y + gaze_vector[1] * gaze_scale
            right_gaze_end = (right_gaze_end_x, right_gaze_end_y)
            
            # Convert to relative coordinates
            rel_left_eye_center = (left_eye_center[0] / width, left_eye_center[1] / height)
            rel_left_gaze_end = (left_gaze_end[0] / width, left_gaze_end[1] / height)
            
            rel_right_eye_center = (right_eye_center[0] / width, right_eye_center[1] / height)
            rel_right_gaze_end = (right_gaze_end[0] / width, right_gaze_end[1] / height)
            
            # Add gaze direction lines
            head_pose_axes.extend([
                fo.Polyline(
                    label="Left Eye Gaze",
                    points=[[rel_left_eye_center, rel_left_gaze_end]],
                    closed=False,
                    filled=False,
                    line_width=2,
                    color="#00ff00",  # Magenta
                ),
                fo.Polyline(
                    label="Right Eye Gaze",
                    points=[[rel_right_eye_center, rel_right_gaze_end]],
                    closed=False,
                    filled=False,
                    line_width=2,
                    color="#00ff00",  # Magenta
                )
            ])'''
    # Add all annotations to the sample
    if detections:
        sample["detections"] = fo.Detections(detections=detections)
    if rel_landmarks:
        sample["keypoints"] = fo.Keypoint(points=rel_landmarks)
    if head_pose_axes:
        sample["head_pose_axes"] = fo.Polylines(polylines=head_pose_axes)
    
    # Add sample to dataset
    dataset.add_sample(sample)

# Launch the FiftyOne App'''
session = fo.launch_app(dataset)
session.wait()