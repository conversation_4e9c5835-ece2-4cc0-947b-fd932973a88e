import json
import cv2
import os
import argparse
import torch
import torch.backends.cudnn as cudnn
from PIL import Image
import torch.nn as nn
import torch.nn.functional as F
import torchvision.transforms as transforms
import numpy as np
import time
from config import cfg_mos_m, cfg_mos_s
from utils.functions.prior_box import PriorBox
from utils.nms.py_cpu_nms import py_cpu_nms
from models.MOS import MOS
from datetime import datetime


parser = argparse.ArgumentParser(description='Test')
parser.add_argument('-m', '--trained_model', default='./test_weights/MOS-M.pth',
                    type=str, help='Trained state_dict file path to open')
parser.add_argument('--network', default='cfg_mos_m', help='Backbone network cfg_mos_m or cfg_mos_s')
parser.add_argument('--origin_size', default=True, type=str, help='Whether use origin image size to evaluate')
parser.add_argument('--long_side', default=840, help='when origin_size is false, long_side is scaled size(320 or 640 for long side)')
parser.add_argument('--cpu', action="store_true", default=True, help='Use cpu inference')
parser.add_argument('--confidence_threshold', default=0.55, type=float, help='confidence_threshold')
parser.add_argument('--top_k', default=5000, type=int, help='top_k')
parser.add_argument('--nms_threshold', default=0.4, type=float, help='nms_threshold')
parser.add_argument('--keep_top_k', default=750, type=int, help='keep_top_k')
parser.add_argument('--vis_thres', default=0.55, type=float, help='visualization_threshold')
parser.add_argument('--output_json', default='mos_predictions.json', type=str, help='Output JSON file path')
parser.add_argument('--save_image', action="store_true", default=False, help='Save visualization images')
args = parser.parse_args()


def load_model(net, model_path, cpu):
    """Load model from path"""
    if cpu:
        pretrained_dict = torch.load(model_path, map_location=lambda storage, loc: storage)
    else:
        device = torch.device("cuda")
        pretrained_dict = torch.load(model_path, map_location=lambda storage, loc: storage.cuda(device))
    
    if "state_dict" in pretrained_dict.keys():
        pretrained_dict = pretrained_dict["state_dict"]
    
    net.load_state_dict(pretrained_dict, strict=False)
    return net


def detect_faces_and_poses(net, img, device, cfg):
    """Detect faces and head poses in an image"""
    img = np.float32(img)
    im_height, im_width, _ = img.shape
    scale = torch.Tensor([img.shape[1], img.shape[0], img.shape[1], img.shape[0]])
    scale = scale.to(device)

    # Preprocess image
    img -= (104, 117, 123)
    img = img.transpose(2, 0, 1)
    img = torch.from_numpy(img).unsqueeze(0)
    img = img.to(device)

    # Forward pass
    loc, conf, landms, head_cls_y, head_cls_p, head_cls_r = net(img)

    # Get priorbox
    priorbox = PriorBox(cfg, image_size=(im_height, im_width))
    priors = priorbox.forward()
    priors = priors.to(device)
    prior_data = priors.data
    
    # Decode bounding boxes and landmarks
    boxes = decode(loc.data.squeeze(0), prior_data, cfg['variance'])
    boxes = boxes * scale
    boxes = boxes.cpu().numpy()
    
    scores = conf.squeeze(0).data.cpu().numpy()[:, 1]
    
    landms = decode_landm(landms.data.squeeze(0), prior_data, cfg['variance'])
    
    # Create scale for landmarks - need to match dimensions
    # The landms tensor has shape [num_priors, 10] where each row has 5 landmarks with (x,y) coordinates
    # So we need to repeat scale tensor to match this format
    scale1 = torch.Tensor([img.shape[3], img.shape[2]]).repeat(5).to(device)  # [w, h, w, h, w, h, w, h, w, h]
    landms = landms * scale1
    landms = landms.cpu().numpy()

    # Decode head pose
    idx_tensor = [idx for idx in range(66)]
    idx_tensor = torch.FloatTensor(idx_tensor).to(device)
    
    head_cls_y = head_cls_y.squeeze(0)
    head_cls_p = head_cls_p.squeeze(0)
    head_cls_r = head_cls_r.squeeze(0)
    
    yaw = torch.sum(head_cls_y * idx_tensor, 1).to(device) * 3 - 99
    pitch = torch.sum(head_cls_p * idx_tensor, 1).to(device) * 3 - 99
    roll = torch.sum(head_cls_r * idx_tensor, 1).to(device) * 3 - 99
    
    yaw = yaw.cpu().numpy()
    pitch = pitch.cpu().numpy()
    roll = roll.cpu().numpy()

    # NMS
    inds = np.where(scores > args.confidence_threshold)[0]
    boxes = boxes[inds]
    landms = landms[inds]
    scores = scores[inds]
    yaw = yaw[inds]
    pitch = pitch[inds]
    roll = roll[inds]

    # Keep top-k before NMS
    order = scores.argsort()[::-1][:args.top_k]
    boxes = boxes[order]
    landms = landms[order]
    scores = scores[order]
    yaw = yaw[order]
    pitch = pitch[order]
    roll = roll[order]

    # Do NMS
    dets = np.hstack((boxes, scores[:, np.newaxis])).astype(np.float32, copy=False)
    keep = py_cpu_nms(dets, args.nms_threshold)
    dets = dets[keep, :]
    landms = landms[keep]
    yaw = yaw[keep]
    pitch = pitch[keep]
    roll = roll[keep]

    # Keep top-k after NMS
    dets = dets[:args.keep_top_k, :]
    landms = landms[:args.keep_top_k, :]
    yaw = yaw[:args.keep_top_k]
    pitch = pitch[:args.keep_top_k]
    roll = roll[:args.keep_top_k]
    
    # Format results
    results = []
    for i in range(dets.shape[0]):
        if dets[i, 4] < args.vis_thres:
            continue
            
        # Convert bbox from [x1, y1, x2, y2] to [x, y, width, height] format for COCO
        x1, y1, x2, y2 = dets[i, :4]
        width = x2 - x1
        height = y2 - y1
        
        face_result = {
            "bbox": [float(x1), float(y1), float(width), float(height)],
            "score": float(dets[i, 4]),
            "landmarks": landms[i, :].reshape(-1, 2).tolist(),
            "head_pose": {
                "yaw": float(yaw[i]),
                "pitch": float(pitch[i]),
                "roll": float(roll[i])
            }
        }
        results.append(face_result)
    
    return results


if __name__ == '__main__':
    torch.set_grad_enabled(False)
    cfg = None
    net = None

    # Import decode functions
    from utils.box_utils import decode, decode_landm

    if args.network == "cfg_mos_m":
        cfg = cfg_mos_m
    elif args.network == "cfg_mos_s":
        cfg = cfg_mos_s

    # Initialize model
    net = MOS(cfg=cfg, phase='test')
    net = load_model(net, args.trained_model, args.cpu)
    net.eval()
    print('Finished loading model!')
    cudnn.benchmark = True
    device = torch.device("cuda" if not args.cpu else "cpu")
    net = net.to(device)

    # Load your COCO formatted JSON
    input_json_path = "data/Autopose/autopose_subject_2.json"
    with open(input_json_path, "r") as f:
        coco_data = json.load(f)
    
    # Create a copy of the input COCO data structure for our output
    output_coco = {
        "info": {
            "description": f"MOS face detection and head pose estimation results based on {os.path.basename(input_json_path)}",
            "url": "",
            "version": "1.0",
            "year": datetime.now().year,
            "contributor": "MOS Model",
            "date_created": datetime.now().strftime("%Y-%m-%d")
        },
        "images": coco_data["images"],  # Keep the same image information
        "categories": coco_data["categories"],  # Keep the same categories
        "annotations": []  # We'll fill this with our detections
    }
    
    # Process each image in the COCO data
    annotation_id = 1
    for img_idx, img_info in enumerate(coco_data["images"]):
        print(f"Processing image {img_idx+1}/{len(coco_data['images'])}: {img_info['file_name']}")
        
        # Read image
        img_path = os.path.join("data/Autopose/train", img_info["file_name"])
        if not os.path.exists(img_path):
            print(f"Warning: Image not found: {img_path}")
            continue
            
        img_raw = cv2.imread(img_path, cv2.IMREAD_COLOR)
        if img_raw is None:
            print(f"Warning: Could not read image: {img_path}")
            continue
            
        img = img_raw.copy()

        # Apply scaling if needed
        target_size = int(args.long_side)
        max_size = int(args.long_side)
        im_shape = img.shape
        im_size_min = np.min(im_shape[0:2])
        im_size_max = np.max(im_shape[0:2])
        resize = float(target_size) / float(im_size_min)
        
        # Prevent bigger axis from being more than max_size
        if np.round(resize * im_size_max) > max_size:
            resize = float(max_size) / float(im_size_max)
        if args.origin_size:
            resize = 1

        if resize != 1:
            img = cv2.resize(img, None, None, fx=resize, fy=resize, interpolation=cv2.INTER_LINEAR)

        # Detect faces and head poses
        face_results = detect_faces_and_poses(net, img, device, cfg)
        
        # Add detections to COCO annotations
        for face_idx, face in enumerate(face_results):
            # Create COCO annotation
            annotation = {
                "id": annotation_id,
                "image_id": img_info["frame_id"],
                "category_id": 1,  # Assuming 1 is the category ID for "person"
                "subject_id": img_info.get("subject_id", 0),
                "frame_id": img_info["frame_id"],
                "face_bbox": {
                    "bbox": face["bbox"],
                    "confidence": face["score"]
                },
                "facial_landmarks": {
                    "count": len(face["landmarks"]),
                    "points": face["landmarks"]
                },
                "head_pose": face["head_pose"]
            }
            
            output_coco["annotations"].append(annotation)
            annotation_id += 1
            
            # Visualize results (optional)
            if args.save_image:
                # Draw bounding box
                x, y, w, h = map(int, face["bbox"])
                print(x, y, w, h)
                cv2.rectangle(img_raw, (x, y), (x + w, y + h), (0, 255, 0), 2)
                
                # Draw landmarks
                landmarks = face["landmarks"]
                for landmark in landmarks:
                    cv2.circle(img_raw, (int(landmark[0]), int(landmark[1])), 2, (0, 0, 255), -1)
                
                # Add head pose text
                pose = face["head_pose"]
                text = f"Y:{pose['yaw']:.1f} P:{pose['pitch']:.1f} R:{pose['roll']:.1f}"
                cv2.putText(img_raw, text, (x, y-10), 
                            cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 0, 0), 2)
        
        # Save visualization (optional)
        if args.save_image and face_results:
            os.makedirs("results", exist_ok=True)
            output_path = os.path.join("results", f"result_{img_info['frame_id']}.jpg")
            cv2.imwrite(output_path, img_raw)
    
    # Save results to JSON file
    with open(args.output_json, 'w') as f:
        json.dump(output_coco, f, indent=2)
    
    print(f"Processed {len(output_coco['images'])} images with {len(output_coco['annotations'])} face detections")
    print(f"Results saved to {args.output_json}")
